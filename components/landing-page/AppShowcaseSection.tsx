"use client";

import { useEffect, useState, useRef } from "react";
import { cn } from "@/lib/utils";

interface AppShowcaseSectionProps {
  className?: string;
}

export function AppShowcaseSection({ className }: AppShowcaseSectionProps) {
  const [scrollProgress, setScrollProgress] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const rect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      
      // Calculate scroll progress based on section visibility
      // 0 = section just entering viewport from bottom
      // 1 = section completely passed through viewport
      const sectionTop = rect.top;
      const sectionHeight = rect.height;
      
      // Start animation later - when section is 60% visible from bottom
      const startPoint = windowHeight * 0.4;
      // End animation when section top reaches viewport top (image should be straight here)
      const endPoint = -windowHeight * 0.2;

      let progress = 0;
      if (sectionTop <= startPoint && sectionTop >= endPoint) {
        progress = (startPoint - sectionTop) / (startPoint - endPoint);
        progress = Math.max(0, Math.min(1, progress));
      } else if (sectionTop < endPoint) {
        progress = 1;
      }
      
      setScrollProgress(progress);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    handleScroll(); // Initial calculation
    
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Calculate transform values based on scroll progress
  const getTransformStyle = () => {
    // Start with image tilted back vertically and slightly scaled down (increased angle for faster rotation)
    const initialRotateX = 50; // degrees (increased from 35 for even faster rotation)
    const initialScale = 0.75; // smaller initial scale for more dramatic effect

    // End with image straight and full scale
    const finalRotateX = 0;
    const finalScale = 1;

    // Interpolate values (no Y rotation for vertical-only effect)
    const rotateX = initialRotateX + (finalRotateX - initialRotateX) * scrollProgress;
    const scale = initialScale + (finalScale - initialScale) * scrollProgress;

    // Add some subtle floating effect
    const translateY = Math.sin(scrollProgress * Math.PI) * 15;

    return {
      transform: `perspective(1200px) rotateX(${rotateX}deg) scale(${scale}) translateY(${translateY}px)`,
      transition: scrollProgress === 0 ? 'transform 0.3s ease-out' : 'none'
    };
  };

  return (
    <section 
      ref={sectionRef}
      className={cn("py-24 bg-gradient-to-b from-gray-50 to-white overflow-hidden", className)}
    >
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
            Experience Market-Me
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            See how our AI-powered platform transforms your product images into stunning marketing campaigns
          </p>
        </div>
        
        <div className="flex justify-center items-center min-h-[600px] md:min-h-[900px]">
          <div
            className="relative w-full max-w-3xl md:max-w-6xl lg:max-w-7xl xl:max-w-8xl 2xl:max-w-9xl mx-auto"
            style={{
              transformStyle: 'preserve-3d',
              ...getTransformStyle()
            }}
          >
            {/* Main app showcase image */}
            <img
              src="/landing-page/app-showcase/app-showcase.webp"
              alt="Market-Me App Showcase - AI Fashion Marketing Platform"
              className="w-full h-auto rounded-2xl shadow-2xl"
              style={{
                filter: `brightness(${0.9 + scrollProgress * 0.1}) contrast(${1 + scrollProgress * 0.1})`
              }}
            />

          </div>
        </div>
        
        {/* Call to action */}
        <div className="text-center mt-16">
          <div 
            className="inline-block"
            style={{
              opacity: 0.5 + scrollProgress * 0.5,
              transform: `translateY(${(1 - scrollProgress) * 20}px)`
            }}
          >
            <p className="text-lg text-gray-600 mb-6">
              Ready to revolutionize your fashion marketing?
            </p>
            <a
              href="/sign-up"
              className="inline-flex items-center px-8 py-3 bg-[#FD2D55] text-white rounded-full text-lg font-medium hover:bg-[#FD2D55]/90 transition-colors shadow-lg hover:shadow-xl"
            >
              Start Creating Now
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
